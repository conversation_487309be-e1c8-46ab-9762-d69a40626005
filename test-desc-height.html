<!DOCTYPE html>
<html>
<head>
    <title>测试描述高度计算</title>
    <style>
        .test-desc {
            width: 720px;
            font-size: 16px;
            line-height: 24px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            border: 1px solid #ccc;
            margin: 20px;
            padding: 10px;
        }
        .test-desc.allShow {
            -webkit-line-clamp: unset;
        }
        .btn {
            margin: 20px;
            padding: 10px;
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>测试描述文本行数计算</h1>

    <div class="test-desc" id="testDesc">
        这是一个测试文本，用来验证行数计算是否正确。这个文本应该会超过3行，所以应该显示展开按钮。
    </div>

    <button class="btn" onclick="calculateLines()">计算行数</button>
    <button class="btn" onclick="toggleExpand()">切换展开</button>

    <div id="result"></div>

    <script>
        function calculateLines() {
            const descDom = document.getElementById('testDesc');

            // 创建临时测量元素
            const tempDiv = document.createElement('div');
            tempDiv.style.position = 'absolute';
            tempDiv.style.visibility = 'hidden';
            tempDiv.style.height = 'auto';
            tempDiv.style.width = descDom.offsetWidth + 'px';
            tempDiv.style.fontSize = window.getComputedStyle(descDom).fontSize;
            tempDiv.style.lineHeight = window.getComputedStyle(descDom).lineHeight;
            tempDiv.style.fontFamily = window.getComputedStyle(descDom).fontFamily;
            tempDiv.style.fontWeight = window.getComputedStyle(descDom).fontWeight;
            tempDiv.innerHTML = descDom.innerHTML;

            document.body.appendChild(tempDiv);

            const lineHeight = parseFloat(window.getComputedStyle(tempDiv).lineHeight);
            const height = tempDiv.offsetHeight;
            const lineCount = Math.round(height / lineHeight);

            document.body.removeChild(tempDiv);

            document.getElementById('result').innerHTML = `
                <p>计算结果：</p>
                <p>文本高度: ${height}px</p>
                <p>行高: ${lineHeight}px</p>
                <p>计算行数: ${lineCount}</p>
                <p>是否应该显示展开按钮: ${lineCount > 3 ? '是' : '否'}</p>
            `;
        }

        function toggleExpand() {
            const descDom = document.getElementById('testDesc');
            descDom.classList.toggle('allShow');
        }
    </script>
</body>
</html>